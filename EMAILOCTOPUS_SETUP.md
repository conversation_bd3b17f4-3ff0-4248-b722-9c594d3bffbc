# 📧 Emailoctopus Newsletter Setup Guide

This guide will help you set up Emailoctopus integration for your TechPriceTrack newsletter system.

## 🚀 Quick Setup Steps

### 1. Create Emailoctopus Account
1. Go to [emailoctopus.com](https://emailoctopus.com) and sign up for a free account
2. Verify your email address
3. Complete the account setup process

### 2. Create a List
1. In your Emailoctopus dashboard, click **Lists**
2. Click **Create a new list**
3. Fill in the required information:
   - **List name**: "TechPriceTrack Pre-Launch"
   - **From name**: "TechPriceTrack Team"
   - **From email address**: <EMAIL>
   - **Reply-to email**: <EMAIL>
   - **Company/Organization**: "TechPriceTrack"
   - **Address**: Your business address
4. Click **Create list**

### 3. Get Your API Key
1. Go to **Settings** → **API**
2. Copy your API key (format: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`)

### 4. Get Your List ID
1. Go to **Lists** and click on your list
2. In the URL, you'll see the List ID (format: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`)
3. Or go to **Settings** → **List settings** to find the List ID

### 5. Configure Environment Variables
Create a `.env.local` file in your project root:

```bash
# Newsletter - Emailoctopus Integration
EMAILOCTOPUS_API_KEY=your_api_key_here
EMAILOCTOPUS_LIST_ID=your_list_id_here

# Other existing variables...
NEXT_PUBLIC_BASE_URL=https://www.techpricetrack.net
NEXT_PUBLIC_GA_ID=G-8GZYMXGPVQ
```

## 🎯 Features Included

### ✅ Newsletter Banner
- Replaces the old yellow development banner
- Maintains the same yellow color scheme for consistency
- Responsive design that works on all devices
- Multi-language support (8 languages)
- Professional design with clear call-to-action

### ✅ Email Validation
- Client-side email format validation
- Server-side validation and sanitization
- Duplicate email prevention
- Error handling with user-friendly messages

### ✅ Emailoctopus Integration
- Automatic subscriber addition to your list
- Tags subscribers with "pre-launch" and "website-signup"
- Adds custom fields (Source, SignupDate)
- Handles Emailoctopus API errors gracefully

### ✅ Multi-Language Support
- Newsletter text translated into 8 languages:
  - English (default)
  - French
  - German
  - Spanish
  - Arabic (RTL support)
  - Chinese
  - Japanese
  - Russian

## 🔧 API Endpoint Details

### POST /api/newsletter
Subscribes an email to your Mailchimp audience.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (201):**
```json
{
  "message": "Successfully subscribed to newsletter!",
  "email": "<EMAIL>"
}
```

**Error Responses:**
- `400`: Invalid email format
- `409`: Email already subscribed
- `500`: Server/Emailoctopus API error

## 🎨 Customization Options

### Banner Styling
The newsletter banner can be customized in `src/components/NewsletterSignup.tsx`:

```tsx
// Change colors (currently matches original yellow banner)
className="bg-yellow-500 text-black"

// Change text
bannerTitle: "Site Under Development - Get Early Access!"
```

### Add Custom Fields
In `src/app/api/newsletter/route.ts`, you can add more fields:

```typescript
fields: {
  FirstName: '',
  LastName: '',
  Source: 'Website Pre-Launch',
  SignupDate: new Date().toISOString().split('T')[0],
  Language: locale, // Add user's language
  Referrer: request.headers.get('referer') // Add referrer
}
```

## 📊 Emailoctopus Dashboard Features

Once set up, you can:
- View subscriber growth in real-time
- Create automated email sequences
- Segment subscribers by tags (pre-launch, website-signup)
- Design email templates with drag-and-drop editor
- Track open rates and click-through rates
- Export subscriber data
- Use Amazon SES for excellent deliverability

## 🚀 Launch Day Preparation

### Email Campaign Ideas:
1. **Welcome Series**: 3-email sequence introducing TechPriceTrack
2. **Launch Announcement**: "We're Live!" email with special offers
3. **Feature Highlights**: Showcase price tracking capabilities
4. **Exclusive Deals**: Early access to best hardware deals

### Recommended Emailoctopus Plan:
- **Free Plan**: Up to 2,500 contacts (perfect for pre-launch)
- **Pro Plan**: $8/month for 5,000 contacts with advanced features
- **Higher tiers**: Very affordable scaling (much cheaper than competitors)

## 🔒 Security & Privacy

### Data Protection:
- Emails are sent directly to Mailchimp (no local storage)
- GDPR compliant with proper consent mechanisms
- Secure API communication with HTTPS
- Environment variables protect sensitive keys

### Privacy Policy:
Make sure to update your privacy policy to mention:
- Email collection for newsletter purposes
- Use of Emailoctopus as email service provider
- User's right to unsubscribe at any time

## 🐛 Troubleshooting

### Common Issues:

**"Newsletter service is not configured"**
- Check that `EMAILOCTOPUS_API_KEY` and `EMAILOCTOPUS_LIST_ID` are set in `.env.local`
- Restart your development server after adding environment variables

**"Invalid Parameters" error**
- Verify your List ID is correct
- Check that your API key is valid

**"Member Exists" error**
- This is normal - the system handles duplicate emails gracefully
- User will see "This email is already subscribed" message

**API key format issues**
- API key should look like: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
- Make sure you're using the correct API key from Settings → API

## 📈 Next Steps

1. **Test the integration** with a few email addresses
2. **Create welcome email templates** in Emailoctopus
3. **Set up automation** for new subscribers
4. **Plan your launch email campaign**
5. **Monitor subscriber growth** leading up to launch

Your newsletter system is now ready to collect pre-launch subscribers! 🎉
