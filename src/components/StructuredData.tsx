import { HardwareProduct } from '@/data/hardwareData';

interface ProductStructuredDataProps {
  product: HardwareProduct;
  locale: string;
}

export function ProductStructuredData({ product }: ProductStructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://hardware-prices.com';
  const productUrl = `${baseUrl}/product/${product.id}`;
  
  // Create offers array from price sources
  const offers = product.prices.map(price => ({
    "@type": "Offer",
    "url": `https://${price.sourceId}`,
    "priceCurrency": "USD",
    "price": price.price.toString(),
    "availability": price.availability === 'In Stock' 
      ? "https://schema.org/InStock"
      : price.availability === 'Limited Stock'
      ? "https://schema.org/LimitedAvailability"
      : "https://schema.org/OutOfStock",
    "seller": {
      "@type": "Organization",
      "name": price.sourceId
    }
  }));

  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.specs,
    "brand": {
      "@type": "Brand",
      "name": product.brand
    },
    "model": product.model,
    "category": product.category,
    "url": productUrl,
    "image": [`${baseUrl}/products/${product.id}.jpg`],
    "offers": offers.length > 0 ? offers : [{
      "@type": "Offer",
      "url": productUrl,
      "priceCurrency": "USD",
      "price": product.currentPrice.toString(),
      "availability": "https://schema.org/InStock"
    }],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": product.rating.toString(),
      "reviewCount": product.reviews.toString(),
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": product.reviews > 0 ? [{
      "@type": "Review",
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": product.rating.toString(),
        "bestRating": "5"
      },
      "author": {
        "@type": "Person",
        "name": "TechPriceTrack Users"
      }
    }] : undefined
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(productSchema, null, 2)
      }}
    />
  );
}

export function OrganizationStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://hardware-prices.com';
  
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "TechPriceTrack",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "description": "Track prices across thousands of PC components and never miss a deal again",
    "sameAs": [
      "https://twitter.com/techpricetrack",
      "https://facebook.com/techpricetrack",
      "https://linkedin.com/company/techpricetrack"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationSchema, null, 2)
      }}
    />
  );
}

export function WebSiteStructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://hardware-prices.com';
  
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "TechPriceTrack",
    "url": baseUrl,
    "description": "Track prices across thousands of PC components and never miss a deal again",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(websiteSchema, null, 2)
      }}
    />
  );
}

interface BreadcrumbStructuredDataProps {
  items: Array<{
    name: string;
    url: string;
  }>;
}

export function BreadcrumbStructuredData({ items }: BreadcrumbStructuredDataProps) {
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbSchema, null, 2)
      }}
    />
  );
}
