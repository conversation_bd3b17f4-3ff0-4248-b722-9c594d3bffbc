'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowLeft,
  Star,
  TrendingDown,
  ExternalLink,
  Globe,
  Calendar,
  Package,
  Award,
  ShoppingCart,
  Heart,
  Share2
} from 'lucide-react';
import {
  calculatePriceChange,
  priceSources,
  type HardwareProduct
} from '@/data/hardwareData';
import { PriceHistoryChart } from '@/components/PriceHistoryChart';
import { PriceComparisonTable } from '@/components/PriceComparisonTable';

interface ProductDetailsClientProps {
  product: HardwareProduct;
}

export function ProductDetailsClient({ product }: ProductDetailsClientProps) {
  const router = useRouter();
  const [selectedTimeframe] = useState('3M');
  
  const priceChange = calculatePriceChange(product);
  const bestPrice = product.prices.length > 0 
    ? Math.min(...product.prices.map(p => p.price))
    : product.currentPrice;
  
  const sortedPrices = [...product.prices].sort((a, b) => a.price - b.price);
  
  const getSourceName = (sourceId: string) => {
    const source = priceSources.find(s => s.id === sourceId);
    return source ? source.name : sourceId;
  };

  const getSourceWebsite = (sourceId: string) => {
    const source = priceSources.find(s => s.id === sourceId);
    return source ? source.website : '#';
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div className="flex-1">
              <h1 className="text-2xl font-bold">{product.name}</h1>
              <p className="text-muted-foreground">{product.brand} • {product.model}</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Heart className="w-4 h-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Product Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Specifications</h3>
                    <p className="text-sm text-muted-foreground">{product.specs}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Category</h3>
                    <Badge variant="secondary">{product.category.toUpperCase()}</Badge>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Rating</h3>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`w-4 h-4 ${
                              i < Math.floor(product.rating) 
                                ? 'text-yellow-400 fill-current' 
                                : 'text-gray-300'
                            }`} 
                          />
                        ))}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {product.rating} ({product.reviews} reviews)
                      </span>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Last Updated</h3>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4" />
                      {new Date().toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Price History Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Price History</CardTitle>
              </CardHeader>
              <CardContent>
                <PriceHistoryChart
                  priceHistory={product.priceHistory}
                  timeframe={selectedTimeframe}
                />
              </CardContent>
            </Card>

            {/* Price Comparison Table */}
            <Card>
              <CardHeader>
                <CardTitle>Price Comparison ({product.prices.length} sources)</CardTitle>
              </CardHeader>
              <CardContent>
                <PriceComparisonTable prices={product.prices} />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Price Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Best Price
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    ${bestPrice.toLocaleString()}
                  </div>
                  {priceChange.isDecrease && (
                    <div className="flex items-center justify-center gap-1 text-sm text-green-600 mt-1">
                      <TrendingDown className="w-4 h-4" />
                      Save ${Math.abs(priceChange.amount).toLocaleString()} 
                      ({Math.abs(parseFloat(priceChange.percentage))}%)
                    </div>
                  )}
                </div>
                
                <Button className="w-full" size="lg">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Buy at Best Price
                </Button>
              </CardContent>
            </Card>

            {/* Price Comparison */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Price Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sortedPrices.map((price, index) => (
                    <div key={price.sourceId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium">{getSourceName(price.sourceId)}</div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              price.availability === 'In Stock' 
                                ? 'text-green-600 border-green-200' 
                                : price.availability === 'Limited Stock'
                                ? 'text-orange-600 border-orange-200'
                                : 'text-red-600 border-red-200'
                            }`}
                          >
                            {price.availability}
                          </Badge>
                          {index === 0 && (
                            <Badge variant="default" className="text-xs bg-green-600">
                              Best
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">${price.price.toLocaleString()}</div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.open(`https://${getSourceWebsite(price.sourceId)}`, '_blank')}
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
