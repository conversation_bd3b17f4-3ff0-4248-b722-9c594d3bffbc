'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NewsletterSignupProps {
  variant?: 'banner' | 'inline' | 'modal';
  className?: string;
  onSuccess?: () => void;
}

export function NewsletterSignup({ 
  variant = 'inline', 
  className,
  onSuccess 
}: NewsletterSignupProps) {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const t = useTranslations('newsletter');

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setErrorMessage(t('emailRequired'));
      setStatus('error');
      return;
    }

    if (!validateEmail(email)) {
      setErrorMessage(t('emailInvalid'));
      setStatus('error');
      return;
    }

    setStatus('loading');
    setErrorMessage('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim().toLowerCase() }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 409) {
          // Email already subscribed
          throw new Error(data.message || t('alreadySubscribed'));
        }
        throw new Error(data.message || t('subscriptionFailed'));
      }

      setStatus('success');
      setEmail('');
      onSuccess?.();
      
      // Reset success state after 5 seconds
      setTimeout(() => {
        setStatus('idle');
      }, 5000);

    } catch (error) {
      setStatus('error');
      setErrorMessage(error instanceof Error ? error.message : t('subscriptionFailed'));
    }
  };

  const renderBannerVariant = () => (
    <div className={cn(
      "bg-yellow-500 text-black py-4 px-4",
      className
    )}>
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex items-center gap-3 text-center md:text-left">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-black/10 rounded-full flex items-center justify-center">
                <Mail className="w-5 h-5 text-black" />
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-lg">
                🚧 {t('bannerTitle')}
              </h3>
              <p className="text-black/80 text-sm">
                {t('bannerDescription')}
              </p>
            </div>
          </div>
          
          <form onSubmit={handleSubmit} className="flex gap-2 w-full md:w-auto min-w-[300px]">
            <div className="flex-1">
              <Input
                type="email"
                placeholder={t('emailPlaceholder')}
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (status === 'error') {
                    setStatus('idle');
                    setErrorMessage('');
                  }
                }}
                className="bg-white/90 border-black/20 text-black placeholder:text-black/60 focus:bg-white"
                disabled={status === 'loading' || status === 'success'}
              />
            </div>
            <Button
              type="submit"
              disabled={status === 'loading' || status === 'success'}
              className="bg-black text-yellow-500 hover:bg-black/90 font-medium px-6"
            >
              {status === 'loading' && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {status === 'success' && <CheckCircle className="w-4 h-4 mr-2" />}
              {status === 'success' ? t('subscribed') : t('notifyMe')}
            </Button>
          </form>
        </div>
        
        {status === 'error' && (
          <div className="mt-3 flex items-center gap-2 text-red-700 text-sm">
            <AlertCircle className="w-4 h-4" />
            {errorMessage}
          </div>
        )}

        {status === 'success' && (
          <div className="mt-3 flex items-center gap-2 text-green-700 text-sm">
            <CheckCircle className="w-4 h-4" />
            {t('successMessage')}
          </div>
        )}
      </div>
    </div>
  );

  const renderInlineVariant = () => (
    <div className={cn("space-y-4", className)}>
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold">{t('title')}</h3>
        <p className="text-muted-foreground">{t('description')}</p>
      </div>
      
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="flex-1">
          <Input
            type="email"
            placeholder={t('emailPlaceholder')}
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              if (status === 'error') {
                setStatus('idle');
                setErrorMessage('');
              }
            }}
            disabled={status === 'loading' || status === 'success'}
          />
        </div>
        <Button
          type="submit"
          disabled={status === 'loading' || status === 'success'}
        >
          {status === 'loading' && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
          {status === 'success' && <CheckCircle className="w-4 h-4 mr-2" />}
          {status === 'success' ? t('subscribed') : t('subscribe')}
        </Button>
      </form>
      
      {status === 'error' && (
        <div className="flex items-center gap-2 text-red-600 text-sm">
          <AlertCircle className="w-4 h-4" />
          {errorMessage}
        </div>
      )}
      
      {status === 'success' && (
        <div className="flex items-center gap-2 text-green-600 text-sm">
          <CheckCircle className="w-4 h-4" />
          {t('successMessage')}
        </div>
      )}
    </div>
  );

  if (variant === 'banner') {
    return renderBannerVariant();
  }

  return renderInlineVariant();
}
