import { NextRequest, NextResponse } from 'next/server';

interface MailchimpResponse {
  id?: string;
  email_address?: string;
  status?: string;
  type?: string;
  title?: string;
  detail?: string;
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate email
    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check for required environment variables
    const apiKey = process.env.MAILCHIMP_API_KEY;
    const listId = process.env.MAILCHIMP_LIST_ID;

    if (!apiKey || !listId) {
      console.error('Missing Mailchimp configuration');
      return NextResponse.json(
        { message: 'Newsletter service is not configured' },
        { status: 500 }
      );
    }

    // Extract datacenter from API key (e.g., us1, us2, etc.)
    const datacenter = apiKey.split('-')[1];
    if (!datacenter) {
      console.error('Invalid Mailchimp API key format');
      return NextResponse.json(
        { message: 'Newsletter service configuration error' },
        { status: 500 }
      );
    }

    // Mailchimp API endpoint
    const url = `https://${datacenter}.api.mailchimp.com/3.0/lists/${listId}/members`;

    // Prepare the request body
    const requestBody = {
      email_address: email.toLowerCase().trim(),
      status: 'subscribed',
      tags: ['pre-launch', 'website-signup'],
      merge_fields: {
        SOURCE: 'Website Pre-Launch',
        SIGNUP_DATE: new Date().toISOString().split('T')[0]
      }
    };

    // Make request to Mailchimp
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    const data: MailchimpResponse = await response.json();

    if (!response.ok) {
      // Handle specific Mailchimp errors
      if (response.status === 400 && data.title === 'Member Exists') {
        return NextResponse.json(
          { message: 'This email is already subscribed to our newsletter.' },
          { status: 409 }
        );
      }

      if (response.status === 400 && data.title === 'Invalid Resource') {
        return NextResponse.json(
          { message: 'Invalid email address format.' },
          { status: 400 }
        );
      }

      console.error('Mailchimp API error:', data);
      return NextResponse.json(
        { message: 'Failed to subscribe. Please try again later.' },
        { status: 500 }
      );
    }

    // Success response
    return NextResponse.json(
      { 
        message: 'Successfully subscribed to newsletter!',
        email: data.email_address 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { message: 'An unexpected error occurred. Please try again.' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  );
}
