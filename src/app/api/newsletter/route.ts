import { NextRequest, NextResponse } from 'next/server';

interface EmailoctopusResponse {
  id?: string;
  email_address?: string;
  status?: string;
  error?: {
    code: string;
    message: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate email
    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check for required environment variables
    const apiKey = process.env.EMAILOCTOPUS_API_KEY;
    const listId = process.env.EMAILOCTOPUS_LIST_ID;

    // Temporary debug logging
    console.log('Environment check:', {
      hasApiKey: !!apiKey,
      hasListId: !!listId,
      apiKeyLength: apiKey?.length,
      listIdLength: listId?.length,
      apiKeyStart: apiKey?.substring(0, 10),
      listIdStart: listId?.substring(0, 10)
    });

    if (!apiKey || !listId) {
      console.error('Missing Emailoctopus configuration', { apiKey: !!apiKey, listId: !!listId });
      return NextResponse.json(
        { message: 'Newsletter service is not configured' },
        { status: 500 }
      );
    }

    // Emailoctopus API endpoint
    const url = `https://emailoctopus.com/api/1.6/lists/${listId}/contacts`;

    // Prepare the request body for Emailoctopus
    const requestBody = {
      api_key: apiKey,
      email_address: email.toLowerCase().trim(),
      fields: {
        FirstName: '',
        LastName: '',
        Source: 'Website Pre-Launch',
        SignupDate: new Date().toISOString().split('T')[0]
      },
      tags: ['pre-launch', 'website-signup']
    };

    // Make request to Emailoctopus
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    const data: EmailoctopusResponse = await response.json();

    if (!response.ok) {
      // Handle specific Emailoctopus errors
      if (data.error) {
        if (data.error.code === 'MEMBER_EXISTS_WITH_EMAIL_ADDRESS') {
          return NextResponse.json(
            { message: 'This email is already subscribed to our newsletter.' },
            { status: 409 }
          );
        }

        if (data.error.code === 'INVALID_PARAMETERS') {
          return NextResponse.json(
            { message: 'Invalid email address format.' },
            { status: 400 }
          );
        }

        console.error('Emailoctopus API error:', data.error);
        return NextResponse.json(
          { message: data.error.message || 'Failed to subscribe. Please try again later.' },
          { status: 500 }
        );
      }

      console.error('Emailoctopus API error:', data);
      return NextResponse.json(
        { message: 'Failed to subscribe. Please try again later.' },
        { status: 500 }
      );
    }

    // Success response
    return NextResponse.json(
      { 
        message: 'Successfully subscribed to newsletter!',
        email: data.email_address 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { message: 'An unexpected error occurred. Please try again.' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { message: 'Method not allowed' },
    { status: 405 }
  );
}
