'use client';

import { useState, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowLeft,
  Star,
  TrendingDown,
  ExternalLink,
  Globe,
  Calendar,
  Package,
  Award,
  ShoppingCart,
  Heart,
  Share2
} from 'lucide-react';
import {
  getProductById,
  calculatePriceChange,
  priceSources
} from '@/data/hardwareData';
import { PriceHistoryChart } from '@/components/PriceHistoryChart';
import { PriceComparisonTable } from '@/components/PriceComparisonTable';

export default function ProductDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;
  
  const [selectedTimeframe, setSelectedTimeframe] = useState('3M');
  
  const product = useMemo(() => {
    return getProductById(productId);
  }, [productId]);

  if (!product) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The product you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <Button onClick={() => router.push('/')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  const priceChange = calculatePriceChange(product);
  const bestPrice = product.prices.length > 0 
    ? Math.min(...product.prices.map(p => p.price))
    : product.currentPrice;
  
  const sortedPrices = [...product.prices].sort((a, b) => a.price - b.price);
  
  const getSourceName = (sourceId: string) => {
    const source = priceSources.find(s => s.id === sourceId);
    return source ? source.name : sourceId;
  };

  const getSourceWebsite = (sourceId: string) => {
    const source = priceSources.find(s => s.id === sourceId);
    return source ? source.website : '#';
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div className="flex-1">
              <h1 className="text-2xl font-bold">{product.name}</h1>
              <p className="text-muted-foreground">{product.brand} • {product.model}</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Heart className="w-4 h-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Product Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Specifications</h3>
                    <p className="text-sm text-muted-foreground">{product.specs}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Category</h3>
                    <Badge variant="secondary">{product.category.toUpperCase()}</Badge>
                  </div>
                </div>
                
                <div className="flex items-center gap-4 pt-4 border-t">
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-semibold">{product.rating}</span>
                    <span className="text-sm text-muted-foreground">
                      ({product.reviews.toLocaleString()} reviews)
                    </span>
                  </div>
                  <Badge 
                    className={
                      product.availability === 'In Stock' 
                        ? 'bg-green-500 hover:bg-green-600' 
                        : product.availability === 'Limited Stock'
                        ? 'bg-orange-500 hover:bg-orange-600'
                        : 'bg-red-500 hover:bg-red-600'
                    }
                  >
                    {product.availability}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Price History Chart */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Price History
                  </CardTitle>
                  <div className="flex gap-2">
                    {['1M', '3M', '6M', '1Y'].map((timeframe) => (
                      <Button
                        key={timeframe}
                        variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedTimeframe(timeframe)}
                      >
                        {timeframe}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <PriceHistoryChart
                  priceHistory={product.priceHistory}
                  timeframe={selectedTimeframe}
                />
              </CardContent>
            </Card>

            {/* Detailed Price Comparison */}
            <PriceComparisonTable prices={product.prices} />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Price Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Best Price
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    ${bestPrice.toLocaleString()}
                  </div>
                  {priceChange.isDecrease && (
                    <div className="flex items-center justify-center gap-1 text-sm text-green-600 mt-1">
                      <TrendingDown className="w-4 h-4" />
                      Save ${Math.abs(priceChange.amount).toLocaleString()} 
                      ({Math.abs(parseFloat(priceChange.percentage))}%)
                    </div>
                  )}
                </div>
                
                <Button className="w-full" size="lg">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Buy at Best Price
                </Button>
              </CardContent>
            </Card>

            {/* Price Comparison */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Price Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sortedPrices.map((price, index) => (
                    <div key={price.sourceId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="font-medium">{getSourceName(price.sourceId)}</div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              price.availability === 'In Stock' 
                                ? 'text-green-600 border-green-200' 
                                : price.availability === 'Limited Stock'
                                ? 'text-orange-600 border-orange-200'
                                : 'text-red-600 border-red-200'
                            }`}
                          >
                            {price.availability}
                          </Badge>
                          {index === 0 && (
                            <Badge variant="default" className="text-xs bg-green-600">
                              Best
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">${price.price.toLocaleString()}</div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.open(`https://${getSourceWebsite(price.sourceId)}`, '_blank')}
                        >
                          <ExternalLink className="w-3 h-3 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
