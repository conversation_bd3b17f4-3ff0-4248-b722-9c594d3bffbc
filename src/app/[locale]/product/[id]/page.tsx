import { getTranslations } from 'next-intl/server';
import { getProductById } from '@/data/hardwareData';
import { ProductDetailsClient } from '@/components/ProductDetailsClient';
import { ProductStructuredData, BreadcrumbStructuredData } from '@/components/StructuredData';
import { locales, defaultLocale } from '@/i18n/config';
import type { Locale } from '@/i18n/config';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

interface ProductPageProps {
  params: Promise<{ locale: string; id: string }>;
}

export async function generateMetadata({
  params
}: ProductPageProps): Promise<Metadata> {
  const { locale, id } = await params;

  // Validate locale
  if (!locales.includes(locale as Locale)) {
    return {};
  }

  const product = getProductById(id);
  if (!product) {
    return {
      title: 'Product Not Found',
      description: 'The requested product could not be found.',
    };
  }

  const t = await getTranslations({ locale, namespace: 'seo' });
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://hardware-prices.com';

  const productTitle = t('productTitle', { productName: product.name });
  const productDescription = t('productDescription', {
    productName: product.name,
    specs: product.specs
  });

  // Generate hreflang alternates for product
  const baseProductUrl = `${baseUrl}/product/${product.id}`;
  const currentUrl = locale === defaultLocale
    ? baseProductUrl
    : `${baseUrl}/${locale}/product/${product.id}`;

  const alternates = {
    canonical: currentUrl,
    languages: Object.fromEntries(
      locales.map((loc) => [
        loc,
        loc === defaultLocale
          ? baseProductUrl
          : `${baseUrl}/${loc}/product/${product.id}`
      ])
    )
  };

  return {
    title: productTitle,
    description: productDescription,
    keywords: `${product.name}, ${product.brand}, ${product.category}, price comparison, hardware deals`,
    metadataBase: new URL(baseUrl),
    alternates,
    openGraph: {
      title: productTitle,
      description: productDescription,
      url: currentUrl,
      siteName: 'TechPriceTrack',
      locale: locale,
      type: 'website',
      images: [
        {
          url: `/products/${product.id}.jpg`,
          width: 800,
          height: 600,
          alt: product.name,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: productTitle,
      description: productDescription,
      images: [`/products/${product.id}.jpg`],
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export function generateStaticParams() {
  // You would typically get this from your data source
  // For now, we'll generate a few sample params
  return locales.flatMap(locale =>
    ['cpu-intel-i9-13900k', 'gpu-rtx-4090', 'ram-corsair-32gb'].map(id => ({
      locale,
      id
    }))
  );
}

export default async function ProductDetailsPage({ params }: ProductPageProps) {
  const { locale, id: productId } = await params;

  // Validate locale
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  const product = getProductById(productId);

  if (!product) {
    notFound();
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://hardware-prices.com';
  const breadcrumbItems = [
    { name: 'Home', url: baseUrl },
    { name: 'Products', url: `${baseUrl}/products` },
    { name: product.category.toUpperCase(), url: `${baseUrl}/category/${product.category}` },
    { name: product.name, url: `${baseUrl}/product/${product.id}` }
  ];

  return (
    <>
      <ProductStructuredData product={product} locale={locale} />
      <BreadcrumbStructuredData items={breadcrumbItems} />
      <ProductDetailsClient product={product} />
    </>
  );
}
