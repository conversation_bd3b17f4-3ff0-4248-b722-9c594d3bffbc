import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Home, Search } from 'lucide-react';
import Link from 'next/link';

export default function NotFound() {
  const t = useTranslations('errors');

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="text-6xl mb-4">🔍</div>
          <CardTitle className="text-2xl font-bold">
            {t('productNotFound')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          <p className="text-muted-foreground">
            {t('productNotFoundDescription')}
          </p>
          
          <div className="flex flex-col gap-2">
            <Button asChild className="w-full">
              <Link href="/">
                <Home className="w-4 h-4 mr-2" />
                {t('backToHome')}
              </Link>
            </Button>
            
            <Button variant="outline" asChild className="w-full">
              <Link href="/search">
                <Search className="w-4 h-4 mr-2" />
                Search Products
              </Link>
            </Button>
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="font-semibold mb-2">Popular Categories</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <Link href="/category/cpu" className="text-primary hover:underline">
                Processors
              </Link>
              <Link href="/category/gpu" className="text-primary hover:underline">
                Graphics Cards
              </Link>
              <Link href="/category/ram" className="text-primary hover:underline">
                Memory
              </Link>
              <Link href="/category/storage" className="text-primary hover:underline">
                Storage
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
