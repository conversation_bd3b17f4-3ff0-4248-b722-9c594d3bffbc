import { NextIntlClientProvider } from 'next-intl';
import { getMessages, getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import Script from 'next/script';
import { locales, getLanguageInfo, defaultLocale } from '@/i18n/config';
import type { Locale } from '@/i18n/config';
import { Analytics } from "@vercel/analytics/next"
import { OrganizationStructuredData, WebSiteStructuredData } from '@/components/StructuredData';
import type { Metadata } from 'next';
import "../globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;

  // Validate locale
  if (!locales.includes(locale as Locale)) {
    return {};
  }

  const t = await getTranslations({ locale, namespace: 'seo' });
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://www.techpricetrack.net';

  // Generate hreflang alternates
  const alternates = {
    canonical: locale === defaultLocale ? baseUrl : `${baseUrl}/${locale}`,
    languages: Object.fromEntries(
      locales.map((loc) => [
        loc,
        loc === defaultLocale ? baseUrl : `${baseUrl}/${loc}`
      ])
    )
  };

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    authors: [{ name: 'TechPriceTrack' }],
    creator: 'TechPriceTrack',
    publisher: 'TechPriceTrack',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(baseUrl),
    alternates,
    openGraph: {
      title: t('title'),
      description: t('description'),
      url: alternates.canonical,
      siteName: 'TechPriceTrack',
      locale: locale,
      type: 'website',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: t('title'),
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
      images: ['/og-image.jpg'],
      creator: '@techpricetrack',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
      yandex: process.env.YANDEX_VERIFICATION,
      yahoo: process.env.YAHOO_VERIFICATION,
    },
  };
}

export default async function LocaleLayout({
  children,
  params
}: LocaleLayoutProps) {
  const { locale } = await params;

  // Ensure that the incoming `locale` is valid
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages({ locale });
  const languageInfo = getLanguageInfo(locale as Locale);
  const gaId = process.env.NEXT_PUBLIC_GA_ID || 'G-8GZYMXGPVQ';

  return (
    <html lang={locale} dir={languageInfo.dir}>
      <head>
        <OrganizationStructuredData />
        <WebSiteStructuredData />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        {/* Google Analytics */}
        <Script
          src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${gaId}');
          `}
        </Script>

        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
        <Analytics />
      </body>
    </html>
  );
}
