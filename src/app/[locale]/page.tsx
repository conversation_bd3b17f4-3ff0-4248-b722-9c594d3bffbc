'use client';

import { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Header } from '@/components/Header';
import { CategoryFilter } from '@/components/CategoryFilter';
import { ProductCard } from '@/components/ProductCard';
import { NewsletterSignup } from '@/components/NewsletterSignup';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Zap,
  Filter,
  Grid3X3,
  List
} from 'lucide-react';
import { hardwareProducts, getProductsByCategory, getFeaturedProducts } from '@/data/hardwareData';

export default function Home() {
  const t = useTranslations();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState('grid');

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let products = selectedCategory === 'all' 
      ? hardwareProducts 
      : getProductsByCategory(selectedCategory);
    
    // Apply search filter
    if (searchTerm) {
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.specs.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply sorting
    products.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.currentPrice - b.currentPrice;
        case 'price-high':
          return b.currentPrice - a.currentPrice;
        case 'rating':
          return b.rating - a.rating;
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });
    
    return products;
  }, [selectedCategory, searchTerm, sortBy]);

  const featuredProducts = getFeaturedProducts();
  const totalSavings = hardwareProducts.reduce((total, product) => {
    const savings = product.originalPrice - product.currentPrice;
    return total + (savings > 0 ? savings : 0);
  }, 0);

  return (
    <div className="min-h-screen bg-background transition-colors duration-300">
      {/* Newsletter Signup Banner */}
      <NewsletterSignup variant="banner" />

      <Header searchTerm={searchTerm} onSearchChange={setSearchTerm} />

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary via-chart-1 to-chart-5 text-primary-foreground">
        <div className="absolute inset-0 bg-black/10 dark:bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16">
          <div className="text-center space-y-6">
            <div className="inline-flex items-center gap-2 bg-white/10 dark:bg-white/5 backdrop-blur-sm rounded-full px-4 py-2 text-sm border border-white/20">
              <Zap className="w-4 h-4" />
              <span>{t('common.realTimePriceTracking')}</span>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold leading-tight">
              {t('common.findTheBest')}
              <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                {t('common.hardwareDeals')}
              </span>
            </h1>

            <p className="text-xl text-primary-foreground/80 max-w-2xl mx-auto">
              {t('common.trackPricesDescription')}
            </p>
            
            <div className="flex flex-wrap justify-center gap-6 mt-8">
              <div className="text-center bg-white/10 dark:bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="text-3xl font-bold">${totalSavings.toLocaleString()}</div>
                <div className="text-primary-foreground/70">{t('common.totalSavingsAvailable')}</div>
              </div>
              <div className="text-center bg-white/10 dark:bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="text-3xl font-bold">{hardwareProducts.length}</div>
                <div className="text-primary-foreground/70">{t('common.productsTracked')}</div>
              </div>
              <div className="text-center bg-white/10 dark:bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="text-3xl font-bold">47</div>
                <div className="text-primary-foreground/70">{t('common.priceDropsToday')}</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Animated background elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-yellow-300/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-chart-2/20 rounded-full animate-pulse"></div>
      </section>

      {/* Featured Deals Section */}
      <section className="container mx-auto px-4 py-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4 text-foreground">🔥 {t('common.hotDeals')}</h2>
          <p className="text-muted-foreground">{t('common.limitedTimeOffers')}</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {featuredProducts.map((product, index) => (
            <div 
              key={product.id}
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <ProductCard product={product} />
            </div>
          ))}
        </div>
      </section>

      {/* Main Content */}
      <section className="container mx-auto px-4 pb-12">
        {/* Category Filter */}
        <div className="mb-8">
          <CategoryFilter 
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
        </div>

        {/* Controls Bar */}
        <Card className="mb-6 border-border bg-card">
          <CardContent className="p-4">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Filter className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-foreground">Sort by:</span>
                  <select 
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="text-sm border border-border rounded px-2 py-1 bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="name">Name</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Rating</option>
                  </select>
                </div>
                
                <Badge variant="secondary" className="bg-secondary text-secondary-foreground">
                  {filteredProducts.length} products
                </Badge>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="transition-colors"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="transition-colors"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        {filteredProducts.length > 0 ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {filteredProducts.map((product, index) => (
              <div 
                key={product.id}
                className="animate-fade-in-up"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold mb-2 text-foreground">No products found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </section>

      {/* Footer */}
      <footer className="bg-card border-t border-border text-card-foreground py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-primary to-chart-2 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold gradient-text">HardwareTracker</span>
              </div>
              <p className="text-muted-foreground">
                Your trusted source for PC hardware price tracking and deals.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4 text-foreground">Categories</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li className="hover:text-foreground transition-colors cursor-pointer">Processors</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Graphics Cards</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Memory</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Storage</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4 text-foreground">Features</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li className="hover:text-foreground transition-colors cursor-pointer">Price Alerts</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Price History</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Deal Notifications</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Wishlist</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4 text-foreground">Support</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li className="hover:text-foreground transition-colors cursor-pointer">Help Center</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Contact Us</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">API Documentation</li>
                <li className="hover:text-foreground transition-colors cursor-pointer">Status Page</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2025 TechPriceTrack. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

