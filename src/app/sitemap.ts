import { MetadataRoute } from 'next'
import { locales, defaultLocale } from '@/i18n/config'
import { hardwareProducts } from '@/data/hardwareData'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://hardware-prices.com'
  
  const routes: MetadataRoute.Sitemap = []
  
  // Add homepage for each locale
  locales.forEach((locale) => {
    const url = locale === defaultLocale ? baseUrl : `${baseUrl}/${locale}`
    routes.push({
      url,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
      alternates: {
        languages: Object.fromEntries(
          locales.map((loc) => [
            loc,
            loc === defaultLocale ? baseUrl : `${baseUrl}/${loc}`
          ])
        )
      }
    })
  })
  
  // Add product pages for each locale
  hardwareProducts.forEach((product) => {
    locales.forEach((locale) => {
      const baseProductUrl = `${baseUrl}/product/${product.id}`
      const url = locale === defaultLocale 
        ? baseProductUrl 
        : `${baseUrl}/${locale}/product/${product.id}`
      
      routes.push({
        url,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
        alternates: {
          languages: Object.fromEntries(
            locales.map((loc) => [
              loc,
              loc === defaultLocale 
                ? baseProductUrl 
                : `${baseUrl}/${loc}/product/${product.id}`
            ])
          )
        }
      })
    })
  })
  
  // Add category pages for each locale
  const categories = ['cpu', 'gpu', 'ram', 'storage', 'motherboard', 'psu']
  categories.forEach((category) => {
    locales.forEach((locale) => {
      const baseCategoryUrl = `${baseUrl}/category/${category}`
      const url = locale === defaultLocale 
        ? baseCategoryUrl 
        : `${baseUrl}/${locale}/category/${category}`
      
      routes.push({
        url,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.7,
        alternates: {
          languages: Object.fromEntries(
            locales.map((loc) => [
              loc,
              loc === defaultLocale 
                ? baseCategoryUrl 
                : `${baseUrl}/${loc}/category/${category}`
            ])
          )
        }
      })
    })
  })
  
  return routes
}
