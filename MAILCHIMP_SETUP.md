# 📧 Mailchimp Newsletter Setup Guide

This guide will help you set up Mailchimp integration for your TechPriceTrack newsletter system.

## 🚀 Quick Setup Steps

### 1. Create Mailchimp Account
1. Go to [mailchimp.com](https://mailchimp.com) and sign up for a free account
2. Verify your email address
3. Complete the account setup process

### 2. Create an Audience (Mailing List)
1. In your Mailchimp dashboard, go to **Audience** → **All contacts**
2. Click **Create Audience**
3. Fill in the required information:
   - **Audience name**: "TechPriceTrack Pre-Launch"
   - **Default from email**: <EMAIL>
   - **Default from name**: "TechPriceTrack Team"
   - **Default subject**: "Welcome to TechPriceTrack!"
   - **Permission reminder**: "You signed up for our pre-launch newsletter at techpricetrack.net"
4. Click **Save**

### 3. Get Your API Key
1. Go to **Account** → **Extras** → **API keys**
2. Click **Create A Key**
3. Copy the generated API key (format: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-us1`)

### 4. Get Your List ID
1. Go to **Audience** → **All contacts**
2. Click **Settings** → **Audience name and defaults**
3. Look for **Audience ID** (format: `xxxxxxxxxx`)

### 5. Configure Environment Variables
Create a `.env.local` file in your project root:

```bash
# Newsletter - Mailchimp Integration
MAILCHIMP_API_KEY=your_api_key_here-us1
MAILCHIMP_LIST_ID=your_list_id_here

# Other existing variables...
NEXT_PUBLIC_BASE_URL=https://www.techpricetrack.net
NEXT_PUBLIC_GA_ID=G-8GZYMXGPVQ
```

## 🎯 Features Included

### ✅ Newsletter Banner
- Replaces the old yellow development banner
- Responsive design that works on all devices
- Multi-language support (8 languages)
- Engaging gradient design with call-to-action

### ✅ Email Validation
- Client-side email format validation
- Server-side validation and sanitization
- Duplicate email prevention
- Error handling with user-friendly messages

### ✅ Mailchimp Integration
- Automatic subscriber addition to your audience
- Tags subscribers with "pre-launch" and "website-signup"
- Adds custom merge fields (SOURCE, SIGNUP_DATE)
- Handles Mailchimp API errors gracefully

### ✅ Multi-Language Support
- Newsletter text translated into 8 languages:
  - English (default)
  - French
  - German
  - Spanish
  - Arabic (RTL support)
  - Chinese
  - Japanese
  - Russian

## 🔧 API Endpoint Details

### POST /api/newsletter
Subscribes an email to your Mailchimp audience.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (201):**
```json
{
  "message": "Successfully subscribed to newsletter!",
  "email": "<EMAIL>"
}
```

**Error Responses:**
- `400`: Invalid email format
- `409`: Email already subscribed
- `500`: Server/Mailchimp API error

## 🎨 Customization Options

### Banner Styling
The newsletter banner can be customized in `src/components/NewsletterSignup.tsx`:

```tsx
// Change colors
className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800"

// Change text
bannerTitle: "Site Under Development - Get Early Access!"
```

### Add Custom Fields
In `src/app/api/newsletter/route.ts`, you can add more merge fields:

```typescript
merge_fields: {
  SOURCE: 'Website Pre-Launch',
  SIGNUP_DATE: new Date().toISOString().split('T')[0],
  LANGUAGE: locale, // Add user's language
  REFERRER: request.headers.get('referer') // Add referrer
}
```

## 📊 Mailchimp Dashboard Features

Once set up, you can:
- View subscriber growth in real-time
- Create automated welcome email sequences
- Segment subscribers by tags (pre-launch, website-signup)
- Design beautiful email templates
- Track open rates and click-through rates
- Export subscriber data

## 🚀 Launch Day Preparation

### Email Campaign Ideas:
1. **Welcome Series**: 3-email sequence introducing TechPriceTrack
2. **Launch Announcement**: "We're Live!" email with special offers
3. **Feature Highlights**: Showcase price tracking capabilities
4. **Exclusive Deals**: Early access to best hardware deals

### Recommended Mailchimp Plan:
- **Free Plan**: Up to 2,000 contacts (perfect for pre-launch)
- **Essentials**: $10/month for advanced features when you grow
- **Standard**: $15/month for automation and A/B testing

## 🔒 Security & Privacy

### Data Protection:
- Emails are sent directly to Mailchimp (no local storage)
- GDPR compliant with proper consent mechanisms
- Secure API communication with HTTPS
- Environment variables protect sensitive keys

### Privacy Policy:
Make sure to update your privacy policy to mention:
- Email collection for newsletter purposes
- Use of Mailchimp as email service provider
- User's right to unsubscribe at any time

## 🐛 Troubleshooting

### Common Issues:

**"Newsletter service is not configured"**
- Check that `MAILCHIMP_API_KEY` and `MAILCHIMP_LIST_ID` are set in `.env.local`
- Restart your development server after adding environment variables

**"Invalid Resource" error**
- Verify your List ID is correct
- Check that your API key matches the datacenter (us1, us2, etc.)

**"Member Exists" error**
- This is normal - the system handles duplicate emails gracefully
- User will see "This email is already subscribed" message

**API key format issues**
- API key should look like: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-us1`
- The datacenter suffix (us1, us2, etc.) is required

## 📈 Next Steps

1. **Test the integration** with a few email addresses
2. **Create welcome email templates** in Mailchimp
3. **Set up automation** for new subscribers
4. **Plan your launch email campaign**
5. **Monitor subscriber growth** leading up to launch

Your newsletter system is now ready to collect pre-launch subscribers! 🎉
